.App {
  text-align: center;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
  max-width: 100%;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
  padding: 0 15px;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Multiple images preview styling */
.images-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.image-preview-item {
  position: relative;
  display: inline-block;
}

.remove-image-button {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ff4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.remove-image-button:hover {
  background: #cc0000;
}

.gift-images-display {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Edit and delete buttons styling */
.edit-button {
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  margin-right: 5px;
  cursor: pointer;
}

.delete-button {
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
}

/* Make buttons more touch-friendly on mobile */
@media (max-width: 768px) {
  .edit-button, .delete-button {
    padding: 8px 12px;
    font-size: 14px;
    margin-bottom: 5px;
  }
}

/* Edit form overlay styling */
.edit-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.edit-form {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-height: 90vh;
  overflow-y: auto;
}

.edit-form h2 {
  margin-top: 0;
  color: #333;
  border-bottom: 1px solid #ddd;
  padding-bottom: 10px;
  margin-bottom: 20px;
  font-size: 1.4rem;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 12px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  -webkit-appearance: none; /* Removes default styling on iOS */
}

.form-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  flex-wrap: wrap;
}

@media (max-width: 480px) {
  .form-buttons {
    flex-direction: column;
  }
  
  .form-buttons button {
    width: 100%;
    margin-right: 0 !important;
    margin-bottom: 10px;
  }
}

.form-buttons button {
  padding: 12px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  min-width: 100px;
}

.form-buttons button:first-child {
  background-color: #4CAF50;
  color: white;
  margin-right: 10px;
}

.cancel-button {
  background-color: #f44336;
  color: white;
}

/* Navigation styles with mobile responsiveness */
.app-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #6D8B74;
  padding: 10px 15px;
  color: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  flex-wrap: wrap;
}

.app-logo {
  font-size: 18px;
  font-weight: bold;
  margin-right: 15px;
}

.nav-links {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  flex-grow: 1;
  flex-wrap: wrap;
}

.nav-links li {
  margin-right: 10px;
  margin-bottom: 5px;
}

.nav-links button {
  background: transparent;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 14px;
  padding: 8px 10px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.nav-links button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.nav-links button.active {
  background-color: rgba(255, 255, 255, 0.3);
  font-weight: bold;
}

.user-actions {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 5px;
}

@media (max-width: 768px) {
  .app-nav {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .app-logo {
    margin-bottom: 10px;
    width: 100%;
    text-align: center;
  }
  
  .nav-links {
    width: 100%;
    justify-content: center;
    margin-bottom: 10px;
  }
  
  .user-actions {
    width: 100%;
    justify-content: center;
    margin-top: 10px;
  }
}

.logout-button {
  background-color: #D9CAB3;
  color: #333;
  border: none;
  border-radius: 4px;
  padding: 10px 16px;
  cursor: pointer;
  font-weight: bold;
  margin-right: 10px;
}

.logout-button:hover {
  background-color: #B8A992;
}

.user-info {
  display: flex;
  align-items: center;
  margin-left: 5px;
}

.user-info span {
  font-weight: bold;
  font-size: 14px;
}

/* Add viewport meta tag to your HTML head */
/* <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"> */

/* Additional mobile optimizations */
@media (max-width: 480px) {
  .user-info {
    font-size: 12px;
    margin-top: 5px;
  }
  
  .edit-form {
    padding: 15px;
  }
  
  .form-group input,
  .form-group select {
    font-size: 16px; /* Prevents zoom on focus in iOS */
  }
}

/* Approval status styling */
.status-cell {
  font-weight: bold;
  text-align: center;
  padding: 4px 8px;
  border-radius: 4px;
}

.status-cell.approved {
  color: #2e7d32;
  background-color: rgba(76, 175, 80, 0.1);
}

.status-cell.pending {
  color: #ed6c02;
  background-color: rgba(255, 193, 7, 0.1);
}

.status-cell.declined {
  color: #d32f2f;
  background-color: rgba(244, 67, 54, 0.1);
}

/* Status row highlighting */
tr.status-approved {
  background-color: rgba(76, 175, 80, 0.05);
}

tr.status-pending {
  background-color: rgba(255, 193, 7, 0.05);
}

tr.status-declined {
  background-color: rgba(244, 67, 54, 0.05);
}

/* Action buttons */
.action-buttons {
  display: flex;
  gap: 5px;
  justify-content: center;
}

.approve-button, 
.decline-button, 
.edit-button, 
.delete-button {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: none;
  transition: all 0.2s;
}

.approve-button {
  background-color: rgba(76, 175, 80, 0.1);
  color: #2e7d32;
}

.approve-button:hover {
  background-color: rgba(76, 175, 80, 0.2);
}

.decline-button {
  background-color: rgba(244, 67, 54, 0.1);
  color: #d32f2f;
}

.decline-button:hover {
  background-color: rgba(244, 67, 54, 0.2);
}

.edit-button {
  background-color: rgba(33, 150, 243, 0.1);
  color: #1976d2;
}

.edit-button:hover {
  background-color: rgba(33, 150, 243, 0.2);
}

.delete-button {
  background-color: rgba(97, 97, 97, 0.1);
  color: #616161;
}

.delete-button:hover {
  background-color: rgba(97, 97, 97, 0.2);
}

/* Stats cards for approval */
.stats-card.approval-approved {
  background-color: rgba(76, 175, 80, 0.2);
  border-left: 5px solid #4CAF50;
}

.stats-card.approval-pending {
  background-color: rgba(255, 193, 7, 0.2);
  border-left: 5px solid #FFC107;
}

.stats-card.approval-declined {
  background-color: rgba(244, 67, 54, 0.2);
  border-left: 5px solid #F44336;
}

/* Toggle buttons styling */
.view-toggle {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  gap: 10px;
}

.toggle-button {
  padding: 8px 12px;
  background-color: #6D8B74;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.toggle-button:hover {
  background-color: #e0e0e0;
}

.toggle-button.active {
  background-color: #6D8B74;
  color: white;
  border-color: #5F7161;
}

.password-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.password-modal {
  background-color: white;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  padding: 1.5rem;
  width: 90%;
  max-width: 500px;
}

.password-modal h2 {
  margin-top: 0;
  color: #3C4F3A;
}

.password-display {
  display: flex;
  margin: 1.5rem 0;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.password-display input {
  flex: 1;
  padding: 0.75rem;
  border: none;
  font-size: 1.25rem;
  font-family: monospace;
  background-color: #f5f5f5;
}

/* Add these styles to your CSS file */

.email-status {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: bold;
}

.email-status.sent {
  background-color: #e8f5e9;
  color: #2e7d32;
  font-weight: bold;
  padding: 3px 8px;
  border-radius: 4px;
}

.email-status.sent::before {
  content: "✓ ";
}

.email-status.not-sent {
  background-color: #fff8e1;
  color: #ff8f00;
}

.email-status.error {
  color: #ff4d4d;
  font-weight: bold;
}

.warning {
  color: #f44336;
  font-weight: bold;
}

/* Improved styles for the table's email sent column */
td.email-sent-cell {
  text-align: center;
}

/* Add a small animation for the email sending process */
@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

.sending-email {
  animation: pulse 1.5s infinite;
  color: #2196f3;
}

/* Gift Registry Styles */
.gift-form-container {
  background-color: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.gift-form-container h2 {
  color: #6D8B74;
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.gift-form {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.gift-form .form-group:last-child {
  grid-column: 1 / -1;
}

.gift-form textarea {
  width: 100%;
  min-height: 80px;
  padding: 0.8rem;
  border-radius: 8px;
  border: 1px solid #ccc;
  font-size: 16px;
  resize: vertical;
}

.add-gift-button {
  background-color: #6D8B74;
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  grid-column: 1 / -1;
  justify-self: end;
}

.add-gift-button:hover {
  background-color: #5A7561;
}

.gift-list-container {
  margin-top: 2rem;
}

.gift-list-container h2 {
  color: #6D8B74;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

@media (max-width: 768px) {
  .gift-form {
    grid-template-columns: 1fr;
  }
}

/* Improve form input focus styles */
.gift-form input:focus,
.gift-form textarea:focus,
.gift-form select:focus {
  outline: none;
  border-color: #6D8B74;
  box-shadow: 0 0 0 2px rgba(109, 139, 116, 0.2);
}

/* Make sure inputs have enough spacing */
.gift-form .form-group {
  margin-bottom: 15px;
}

/* Ensure the form is properly laid out on mobile */
@media (max-width: 768px) {
  .gift-form {
    display: flex;
    flex-direction: column;
  }
  
  .gift-form .form-group {
    margin-bottom: 15px;
  }
  
  .add-gift-button {
    align-self: flex-end;
  }
}

/* Image upload styles */
.image-preview {
  margin-top: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 5px;
  background-color: #f9f9f9;
}

.no-image {
  width: 50px;
  height: 50px;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: #999;
  border-radius: 4px;
}

/* Make the file input more attractive */
input[type="file"] {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 100%;
  background-color: #f9f9f9;
}

/* Disabled button style */
button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

/* Edit form buttons */
.form-buttons {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 15px;
}

.save-gift-button {
  background-color: #6D8B74;
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
}

.save-gift-button:hover {
  background-color: #5A7561;
}

.cancel-button {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
}

.cancel-button:hover {
  background-color: #e0e0e0;
}

/* Highlight the row being edited */
tr.editing {
  background-color: rgba(109, 139, 116, 0.1);
}

/* Table Seating Styles */
.table-seating-container {
  max-width: 1200px;
}

.seating-layout {
  display: flex;
  gap: 2rem;
  margin-top: 1.5rem;
}

.tables-section, .guests-section {
  flex: 1;
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tables-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
  max-height: 500px;
  overflow-y: auto;
}

.table-card {
  background: #f9f9f9;
  border: 2px solid #ddd;
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.table-card.selected {
  border-color: #6D8B74;
  background-color: rgba(109, 139, 116, 0.1);
}

.table-card h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  color: #333;
}

.seat-count {
  font-size: 0.9rem;
  color: #666;
}

.table-guests {
  margin-top: 1rem;
  border-top: 1px solid #ddd;
  padding-top: 0.5rem;
}

.table-guests ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.table-guests li {
  font-size: 0.85rem;
  padding: 0.3rem 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.remove-guest-btn {
  background: none;
  border: none;
  color: #d32f2f;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 0;
  margin-left: 0.5rem;
  min-height: auto;
}

.empty-table {
  font-style: italic;
  color: #999;
  font-size: 0.85rem;
  margin: 0.5rem 0 0 0;
}

.guests-section {
  display: flex;
  flex-direction: column;
}

.search-container {
  position: relative;
  margin-bottom: 1rem;
}

.guest-search {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  width: 100%;
  box-sizing: border-box;
  padding-right: 2.5rem; /* Make room for clear button */
}

.guest-search:focus {
  outline: none;
  border-color: #6D8B74;
  box-shadow: 0 0 0 2px rgba(109, 139, 116, 0.2);
}

.clear-search-button {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #666;
  padding: 0.25rem;
  border-radius: 50%;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-search-button:hover {
  background-color: #f0f0f0;
  color: #333;
}

.available-guests {
  overflow-y: auto;
  max-height: 500px;
}

.guest-card {
  background: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 0.8rem;
  margin-bottom: 0.8rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.guest-info {
  display: flex;
  flex-direction: column;
}

.guest-name {
  font-weight: bold;
}

.assigned-table {
  font-size: 0.8rem;
  color: #6D8B74;
  margin-top: 0.3rem;
}

.assign-button {
  background-color: #6D8B74;
  color: white;
  border: none;
  padding: 0.5rem 0.8rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
}

.assign-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.no-guests {
  color: #999;
  font-style: italic;
  text-align: center;
  margin-top: 2rem;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .seating-layout {
    flex-direction: column;
  }
  
  .tables-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }
  
  .guest-card {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .assign-button {
    margin-top: 0.8rem;
    width: 100%;
  }
}

/* Email List Styles */
.email-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.email-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.copy-button {
  background-color: #6D8B74;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.copy-button:hover {
  background-color: #5A7561;
  transform: translateY(-1px);
}

.copy-button.approved {
  background-color: #4CAF50;
}

.copy-button.approved:hover {
  background-color: #43A047;
}

.export-button {
  background-color: #D9CAB3;
  color: #333;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.export-button:hover {
  background-color: #B8A992;
  transform: translateY(-1px);
}

.email-list-container {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.email-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
}

.email-table th,
.email-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.email-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.email-table tbody tr:hover {
  background-color: #f8f9fa;
}

.email-cell {
  max-width: 250px;
  word-break: break-all;
}

.email-link {
  color: #6D8B74;
  text-decoration: none;
  font-weight: 500;
}

.email-link:hover {
  color: #5A7561;
  text-decoration: underline;
}

.copy-single-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.copy-single-button:hover {
  background-color: #f0f0f0;
}

/* Responsive design for email list */
@media (max-width: 768px) {
  .email-stats {
    flex-direction: column;
  }

  .email-actions {
    flex-direction: column;
  }

  .copy-button,
  .export-button {
    width: 100%;
    text-align: center;
  }

  .email-table {
    font-size: 0.85rem;
  }

  .email-table th,
  .email-table td {
    padding: 0.5rem;
  }

  .email-cell {
    max-width: 150px;
  }
}

/* Email List Styles */
.email-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.email-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.copy-button {
  background-color: #6D8B74;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.copy-button:hover {
  background-color: #5A7561;
  transform: translateY(-1px);
}

.copy-button.approved {
  background-color: #4CAF50;
}

.copy-button.approved:hover {
  background-color: #43A047;
}

.export-button {
  background-color: #D9CAB3;
  color: #333;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.export-button:hover {
  background-color: #B8A992;
  transform: translateY(-1px);
}

.email-list-container {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.email-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
}

.email-table th,
.email-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.email-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.email-table tbody tr:hover {
  background-color: #f8f9fa;
}

.email-cell {
  max-width: 250px;
  word-break: break-all;
}

.email-link {
  color: #6D8B74;
  text-decoration: none;
  font-weight: 500;
}

.email-link:hover {
  color: #5A7561;
  text-decoration: underline;
}

.copy-single-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.copy-single-button:hover {
  background-color: #f0f0f0;
}

/* Responsive design for email list */
@media (max-width: 768px) {
  .email-stats {
    flex-direction: column;
  }

  .email-actions {
    flex-direction: column;
  }

  .copy-button,
  .export-button {
    width: 100%;
    text-align: center;
  }

  .email-table {
    font-size: 0.85rem;
  }

  .email-table th,
  .email-table td {
    padding: 0.5rem;
  }

  .email-cell {
    max-width: 150px;
  }
}

/* Late Guest Badge */
.late-guest-badge {
  margin-left: 0.5rem;
  font-size: 0.9rem;
  opacity: 0.8;
  cursor: help;
}
